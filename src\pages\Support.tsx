import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { 
  Send,
  Mail,
  MessageCircle,
  HelpCircle,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Paperclip,
  X
} from "lucide-react";
import { toast } from "@/hooks/use-toast";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Navigate } from "react-router-dom";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface SupportTicket {
  id: string;
  subject: string;
  status: "open" | "in-progress" | "resolved";
  priority: "low" | "medium" | "high";
  createdAt: string;
  lastUpdate: string;
}

const Support = () => {
  const { isAuthenticated, user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    email: user?.email || "",
    subject: "",
    category: "",
    priority: "medium",
    message: "",
  });
  const [attachments, setAttachments] = useState<File[]>([]);
  const [tickets, setTickets] = useState<SupportTicket[]>([
    {
      id: "TICK-001",
      subject: "Video processing taking too long",
      status: "in-progress",
      priority: "medium",
      createdAt: "2024-01-15T10:30:00Z",
      lastUpdate: "2024-01-16T14:20:00Z"
    },
    {
      id: "TICK-002", 
      subject: "Unable to download clips",
      status: "resolved",
      priority: "high",
      createdAt: "2024-01-14T09:15:00Z",
      lastUpdate: "2024-01-14T16:45:00Z"
    }
  ]);

  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles = files.filter(file => {
      const maxSize = 10 * 1024 * 1024; // 10MB
      const allowedTypes = ['image/', 'video/', 'text/', 'application/pdf'];
      
      if (file.size > maxSize) {
        toast({
          title: "File too large",
          description: `${file.name} is larger than 10MB`,
          variant: "destructive"
        });
        return false;
      }
      
      if (!allowedTypes.some(type => file.type.startsWith(type))) {
        toast({
          title: "Invalid file type",
          description: `${file.name} is not a supported file type`,
          variant: "destructive"
        });
        return false;
      }
      
      return true;
    });

    setAttachments(prev => [...prev, ...validFiles]);
  };

  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.subject.trim() || !formData.message.trim()) {
      toast({
        title: "Missing information",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create new ticket
      const newTicket: SupportTicket = {
        id: `TICK-${String(tickets.length + 1).padStart(3, '0')}`,
        subject: formData.subject,
        status: "open",
        priority: formData.priority as "low" | "medium" | "high",
        createdAt: new Date().toISOString(),
        lastUpdate: new Date().toISOString()
      };
      
      setTickets(prev => [newTicket, ...prev]);
      
      // Reset form
      setFormData({
        email: user?.email || "",
        subject: "",
        category: "",
        priority: "medium",
        message: "",
      });
      setAttachments([]);
      
      toast({
        title: "Support ticket created",
        description: `Your ticket ${newTicket.id} has been submitted successfully. We'll get back to you within 24 hours.`
      });
      
    } catch (error) {
      toast({
        title: "Submission failed",
        description: "Failed to submit your support request. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      open: { variant: "outline" as const, color: "text-blue-600", icon: AlertCircle },
      "in-progress": { variant: "secondary" as const, color: "text-yellow-600", icon: Clock },
      resolved: { variant: "default" as const, color: "text-green-600", icon: CheckCircle }
    };
    
    const config = variants[status as keyof typeof variants];
    const Icon = config.icon;
    
    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.replace("-", " ")}
      </Badge>
    );
  };

  const getPriorityBadge = (priority: string) => {
    const colors = {
      low: "bg-green-100 text-green-800",
      medium: "bg-yellow-100 text-yellow-800", 
      high: "bg-red-100 text-red-800"
    };
    
    return (
      <Badge className={colors[priority as keyof typeof colors]}>
        {priority}
      </Badge>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const faqItems = [
    {
      question: "How long does video processing take?",
      answer: "Video processing typically takes 1-3 minutes for videos under 10 minutes. Longer videos may take up to 10 minutes depending on complexity and current server load."
    },
    {
      question: "What video formats are supported?",
      answer: "We support MP4, MOV, AVI, and WebM formats. For best results, we recommend MP4 files with H.264 encoding."
    },
    {
      question: "Can I process videos from YouTube or TikTok?",
      answer: "Yes! You can paste URLs from YouTube, TikTok, Instagram, Twitter, and Facebook. Our system will automatically download and process the video."
    },
    {
      question: "What's the maximum video length?",
      answer: "Free accounts can process videos up to 10 minutes. Pro accounts can process videos up to 60 minutes, and Enterprise accounts have no limits."
    },
    {
      question: "How do I download my clips?",
      answer: "After processing, you can download individual clips or use the 'Download All' button. Clips are available in multiple formats and qualities."
    },
    {
      question: "Can I edit the generated clips?",
      answer: "Yes! Click the 'Edit' button on any clip to access our timeline editor where you can add text overlays, trim clips, and apply effects."
    },
    {
      question: "What makes a clip have a high vitality score?",
      answer: "Our AI analyzes factors like visual engagement, audio peaks, scene changes, and content patterns to predict viral potential. Scores above 80% typically perform well on social media."
    },
    {
      question: "How do I cancel my subscription?",
      answer: "You can cancel your subscription anytime from your account settings. Your access will continue until the end of your current billing period."
    }
  ];

  return (
    <DashboardLayout>
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="text-center space-y-2">
            <h1 className="text-3xl font-bold">Support Center</h1>
            <p className="text-muted-foreground">
              Get help with SmartClips or submit a support request
            </p>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4 text-center">
                <Clock className="h-8 w-8 mx-auto text-blue-500 mb-2" />
                <div className="text-2xl font-bold">< 24h</div>
                <div className="text-sm text-muted-foreground">Average Response Time</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <CheckCircle className="h-8 w-8 mx-auto text-green-500 mb-2" />
                <div className="text-2xl font-bold">98%</div>
                <div className="text-sm text-muted-foreground">Resolution Rate</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4 text-center">
                <MessageCircle className="h-8 w-8 mx-auto text-purple-500 mb-2" />
                <div className="text-2xl font-bold">{tickets.length}</div>
                <div className="text-sm text-muted-foreground">Your Tickets</div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Contact Form */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Mail className="h-5 w-5" />
                    Submit Support Request
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Email Address</label>
                        <Input
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          required
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Category</label>
                        <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="technical">Technical Issue</SelectItem>
                            <SelectItem value="billing">Billing & Subscription</SelectItem>
                            <SelectItem value="feature">Feature Request</SelectItem>
                            <SelectItem value="account">Account Management</SelectItem>
                            <SelectItem value="other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium">Subject *</label>
                        <Input
                          value={formData.subject}
                          onChange={(e) => handleInputChange("subject", e.target.value)}
                          placeholder="Brief description of your issue"
                          required
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium">Priority</label>
                        <Select value={formData.priority} onValueChange={(value) => handleInputChange("priority", value)}>
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="low">Low</SelectItem>
                            <SelectItem value="medium">Medium</SelectItem>
                            <SelectItem value="high">High</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium">Message *</label>
                      <Textarea
                        value={formData.message}
                        onChange={(e) => handleInputChange("message", e.target.value)}
                        placeholder="Please provide detailed information about your issue, including steps to reproduce if applicable..."
                        rows={6}
                        required
                      />
                    </div>

                    <div>
                      <label className="text-sm font-medium">Attachments</label>
                      <div className="mt-2">
                        <input
                          type="file"
                          multiple
                          onChange={handleFileUpload}
                          className="hidden"
                          id="file-upload"
                          accept="image/*,video/*,text/*,.pdf"
                        />
                        <label
                          htmlFor="file-upload"
                          className="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors"
                        >
                          <div className="text-center">
                            <Paperclip className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                            <p className="text-sm text-gray-600">
                              Click to upload files or drag and drop
                            </p>
                            <p className="text-xs text-gray-500">
                              Images, videos, documents (max 10MB each)
                            </p>
                          </div>
                        </label>
                      </div>

                      {attachments.length > 0 && (
                        <div className="mt-3 space-y-2">
                          {attachments.map((file, index) => (
                            <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <div className="flex items-center gap-2">
                                <FileText className="h-4 w-4 text-gray-500" />
                                <span className="text-sm">{file.name}</span>
                                <span className="text-xs text-gray-500">
                                  ({(file.size / 1024 / 1024).toFixed(1)} MB)
                                </span>
                              </div>
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                onClick={() => removeAttachment(index)}
                              >
                                <X className="h-4 w-4" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>

                    <Button type="submit" disabled={isSubmitting} className="w-full">
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Submit Request
                        </>
                      )}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* FAQ and Tickets */}
            <div className="space-y-6">
              {/* Your Tickets */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageCircle className="h-5 w-5" />
                    Your Support Tickets
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {tickets.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No support tickets yet</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {tickets.map((ticket) => (
                        <div key={ticket.id} className="border rounded-lg p-4">
                          <div className="flex items-start justify-between mb-2">
                            <div className="flex-1">
                              <h4 className="font-medium">{ticket.subject}</h4>
                              <p className="text-sm text-muted-foreground">#{ticket.id}</p>
                            </div>
                            <div className="flex gap-2">
                              {getStatusBadge(ticket.status)}
                              {getPriorityBadge(ticket.priority)}
                            </div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Created: {formatDate(ticket.createdAt)}
                            <br />
                            Updated: {formatDate(ticket.lastUpdate)}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* FAQ */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <HelpCircle className="h-5 w-5" />
                    Frequently Asked Questions
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Accordion type="single" collapsible className="w-full">
                    {faqItems.map((item, index) => (
                      <AccordionItem key={index} value={`item-${index}`}>
                        <AccordionTrigger className="text-left">
                          {item.question}
                        </AccordionTrigger>
                        <AccordionContent>
                          {item.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default Support;
