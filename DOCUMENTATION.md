# 📚 SmartClips Complete Documentation

## 🏗️ Project Overview

SmartClips is a comprehensive AI-powered video creation and editing platform that combines React frontend with FastAPI backend. It provides intelligent video clipping, avatar creation, script generation, and social media integration.

## 📁 Project Structure

```
smartclips/
├── 🎨 Frontend (React + TypeScript)
│   ├── src/
│   │   ├── components/     # Reusable UI components
│   │   ├── pages/          # Page components
│   │   ├── context/        # React context providers
│   │   ├── services/       # API service functions
│   │   ├── hooks/          # Custom React hooks
│   │   ├── lib/            # Utility functions
│   │   └── integrations/   # Third-party integrations
│   ├── public/             # Static assets
│   └── backend/            # Python FastAPI backend
├── 🐳 Docker & Deployment
│   ├── docker-compose.yml  # Container orchestration
│   ├── Dockerfile          # Backend container config
│   └── vercel.json         # Frontend deployment config
└── ⚙️ Configuration
    ├── package.json        # Frontend dependencies
    ├── requirements.txt    # Backend dependencies
    ├── tailwind.config.ts  # Styling configuration
    └── vite.config.ts      # Build tool configuration
```

---

## 🎨 Frontend Architecture

### 📄 Core Files

#### `src/App.tsx`
**Purpose**: Main application component and routing configuration
**Key Features**:
- React Router setup with all application routes
- Authentication provider wrapper
- Global layout components (Navbar, Toaster)
- Route protection and navigation structure

**Routes Defined**:
- `/` - HomePage (dashboard for authenticated users)
- `/landing` - Landing page for visitors
- `/login` & `/register` - Authentication pages
- `/dashboard` - Main dashboard
- `/smart-clipper` - AI video clipping tool
- `/avatar-creator` - AI avatar video creation
- `/script-generator` - AI script generation
- `/video-generator` - Complete video generation
- `/gallery` - Video management and library
- `/analytics` - Performance analytics dashboard
- `/settings` - User settings and preferences

#### `src/main.tsx`
**Purpose**: Application entry point
**Key Features**:
- React DOM rendering
- Router provider setup
- Global CSS imports
- Development mode configuration

### 🧩 Components Directory

#### `src/components/ui/`
**Purpose**: Shadcn UI component library
**Components Include**:
- `button.tsx` - Customizable button component
- `card.tsx` - Card container component
- `input.tsx` - Form input component
- `select.tsx` - Dropdown selection component
- `table.tsx` - Data table component
- `tabs.tsx` - Tabbed interface component
- `badge.tsx` - Status and label badges
- `dropdown-menu.tsx` - Context menu component
- `dialog.tsx` - Modal dialog component
- `toast.tsx` - Notification system

#### `src/components/Dashboard.tsx`
**Purpose**: Main dashboard layout with sidebar navigation
**Key Features**:
- Responsive sidebar with collapsible menu
- Navigation sections: Main, Create, Admin, Integrations
- User profile and authentication status
- Theme toggle integration
- Credit balance display
- Role-based menu items (admin features)

#### `src/components/Navbar.tsx`
**Purpose**: Top navigation bar for public pages
**Key Features**:
- Responsive design with mobile menu
- Authentication state handling
- Quick access to main features
- Theme toggle
- Social authentication buttons

#### `src/components/VideoUploader.tsx` & `VideoUploaderWithAPI.tsx`
**Purpose**: Video file upload and processing
**Key Features**:
- Drag-and-drop file upload
- File validation and preview
- Progress tracking
- API integration for video processing
- Error handling and user feedback

### 📱 Pages Directory

#### `src/pages/HomePage.tsx`
**Purpose**: Main dashboard homepage for authenticated users
**Key Features**:
- Personalized welcome message
- Statistics overview cards (videos, views, credits)
- Featured tools with badges (Smart Clipper, Avatar Creator, Script Generator)
- Quick action buttons for common tasks
- Getting started guide with step-by-step instructions
- Responsive grid layout

#### `src/pages/Analytics.tsx`
**Purpose**: Comprehensive analytics dashboard with dual tabs
**Key Features**:
- **Video Performance Tab**:
  - Total views, engagement rate, CTR, conversion rate metrics
  - Detailed video performance table with thumbnails
  - Platform-specific badges (YouTube, TikTok, Instagram, LinkedIn)
  - SMMA-focused metrics (likes, comments, shares, saves)
  - Revenue tracking and CPM data
  - Search and sort functionality
- **Affiliate Analytics Tab**:
  - Revenue tracking and commission data
  - Affiliate link management
  - Customer referral metrics
  - Recent referrals table

#### `src/pages/Gallery.tsx`
**Purpose**: Video library and management system
**Key Features**:
- Grid and list view modes
- Advanced search and filtering
- Video status badges (processing, completed, failed)
- Platform categorization
- Bulk actions and individual video controls
- Responsive design with thumbnail previews

#### `src/pages/SmartClipper.tsx`
**Purpose**: AI-powered video clipping tool
**Key Features**:
- Video upload and processing
- AI-based segment identification
- Clip preview and editing
- Export options
- Batch processing capabilities

#### `src/pages/AvatarCreator.tsx`
**Purpose**: AI avatar video creation
**Key Features**:
- Avatar selection and customization
- Script input and editing
- Voice selection (ElevenLabs integration)
- Video generation and preview
- Export and sharing options

#### `src/pages/VideoGenerator.tsx`
**Purpose**: Complete AI video generation from prompts
**Key Features**:
- Multi-step generation process
- Script generation (OpenAI integration)
- Image generation for scenes
- Audio narration creation
- Video compilation and rendering
- Platform-specific optimization

#### `src/pages/ScriptGenerator.tsx`
**Purpose**: AI-powered script creation tool
**Key Features**:
- Topic and style selection
- AI script generation
- Script editing and refinement
- Export options
- Template library

### 🔧 Services Directory

#### `src/services/videoProcessingService.ts`
**Purpose**: Video upload and processing API integration
**Key Functions**:
- `uploadVideo()` - Upload video files to backend
- `processVideo()` - Trigger AI processing
- Mock data handling for development
- Error handling and retry logic

#### `src/services/videoManagementService.ts`
**Purpose**: Video CRUD operations
**Key Functions**:
- `listVideos()` - Fetch user's video library
- `deleteVideo()` - Remove videos
- `updateVideo()` - Modify video metadata
- `getVideoDetails()` - Fetch detailed video information

#### `src/services/openAiService.ts`
**Purpose**: OpenAI API integration
**Key Functions**:
- Script generation
- Image creation
- Content optimization
- API key management

### 🎯 Context & State Management

#### `src/context/AuthContext.tsx`
**Purpose**: Global authentication state management
**Key Features**:
- Supabase authentication integration
- User session management
- JWT token handling
- Social authentication (Google, Facebook, Apple)
- User profile management
- Admin role detection
- Credit system integration

### 🔗 Integrations

#### `src/integrations/supabase/`
**Purpose**: Supabase backend integration
**Key Features**:
- Database schema definitions
- Authentication configuration
- Real-time subscriptions
- File storage integration

#### `src/lib/supabase.ts`
**Purpose**: Supabase client configuration
**Key Features**:
- Client initialization
- Environment variable handling
- Error handling
- Type safety

---

## 🐍 Backend Architecture

### 📄 Core Files

#### `backend/main.py`
**Purpose**: FastAPI application entry point and API routes
**Key Features**:
- **Authentication System**:
  - JWT token generation and validation
  - User registration and login
  - Password hashing with bcrypt
  - OAuth2 integration
- **Video Processing Endpoints**:
  - `/upload` - Video file upload and processing
  - `/generate` - AI script generation
  - `/generate-audio` - Text-to-speech conversion
  - `/generate-image` - AI image generation
  - `/generate-video` - Video compilation
- **User Management**:
  - User CRUD operations
  - Profile management
  - Subscription handling
- **AI Integrations**:
  - OpenAI API for script and image generation
  - ElevenLabs for voice synthesis
  - Google Cloud TTS as alternative

#### `backend/models.py`
**Purpose**: SQLAlchemy database models
**Key Models**:
- **User Model**:
  - `id`, `username`, `email`, `hashed_password`
  - `avatar_url`, `bio`, `subscription`
  - `created_at`, `updated_at`
  - Relationship to videos
- **Video Model**:
  - `id`, `title`, `description`, `file_path`
  - `thumbnail_url`, `duration`, `status`
  - `user_id` (foreign key)
  - Processing metadata
- **VideoClip Model**:
  - `id`, `video_id`, `start_time`, `end_time`
  - `clip_url`, `title`, `description`
  - AI-generated metadata

#### `backend/database.py`
**Purpose**: Database configuration and connection
**Key Features**:
- SQLAlchemy engine setup
- Session management
- Environment-based database URL
- Connection pooling
- Migration support

#### `backend/video_processing.py`
**Purpose**: Video processing and AI analysis
**Key Functions**:
- `transcribe_video()` - Audio transcription
- `identify_segments()` - AI-powered segment detection
- `extract_clips()` - Video clipping and export
- `analyze_content()` - Content analysis for optimization
- `process_video_pipeline()` - Complete processing workflow

#### `backend/storage.py`
**Purpose**: File storage management
**Key Features**:
- Cloudinary integration for video storage
- AWS S3 support as alternative
- Local file system fallback
- File upload validation
- URL generation and management

### 🔧 Configuration Files

#### `backend/requirements.txt`
**Purpose**: Python dependencies
**Key Packages**:
- `fastapi` - Web framework
- `uvicorn` - ASGI server
- `sqlalchemy` - ORM
- `alembic` - Database migrations
- `moviepy` - Video processing
- `openai` - AI integration
- `cloudinary` - Media storage
- `speech_recognition` - Audio transcription
- `passlib` - Password hashing
- `python-jose` - JWT handling

#### `backend/.env`
**Purpose**: Environment variables
**Key Variables**:
- `SECRET_KEY` - JWT signing key
- `DATABASE_URL` - Database connection
- `OPENAI_API_KEY` - OpenAI integration
- `ELEVENLABS_API_KEY` - Voice synthesis
- `CLOUDINARY_*` - Media storage credentials
- `AWS_*` - S3 storage credentials

---

## 🐳 Deployment & DevOps

### Docker Configuration

#### `docker-compose.yml`
**Purpose**: Multi-container application orchestration
**Services**:
- **backend**: FastAPI application container
- **mongodb**: Database container
- **redis**: Caching layer (optional)
**Features**:
- Environment variable injection
- Volume mounting for persistent data
- Network configuration
- Health checks

#### `backend/Dockerfile`
**Purpose**: Backend container configuration
**Key Steps**:
- Python 3.9 base image
- System dependencies (FFmpeg, etc.)
- Python package installation
- Application code copying
- Port exposure and startup command

### Deployment Configurations

#### `vercel.json`
**Purpose**: Frontend deployment on Vercel
**Features**:
- Build configuration
- Route handling
- Environment variable setup
- Static file serving

#### `backend/render.yaml`
**Purpose**: Backend deployment on Render
**Features**:
- Service configuration
- Environment variable management
- Build and start commands
- Health check endpoints

---

## 🔧 Configuration Files

### Frontend Configuration

#### `package.json`
**Purpose**: Frontend dependencies and scripts
**Key Dependencies**:
- `react` & `react-dom` - Core React
- `typescript` - Type safety
- `vite` - Build tool
- `tailwindcss` - Styling
- `@radix-ui/*` - UI components
- `react-router-dom` - Routing
- `@supabase/supabase-js` - Backend integration

#### `vite.config.ts`
**Purpose**: Build tool configuration
**Features**:
- React plugin setup
- Path aliases (@/ for src/)
- Development server configuration
- Build optimization

#### `tailwind.config.ts`
**Purpose**: Styling framework configuration
**Features**:
- Custom color scheme
- Component styling
- Responsive breakpoints
- Dark mode support

#### `tsconfig.json`
**Purpose**: TypeScript configuration
**Features**:
- Strict type checking
- Path mapping
- Module resolution
- Build targets

### Backend Configuration

#### `backend/database.py`
**Purpose**: Database connection and ORM setup
**Features**:
- SQLAlchemy configuration
- Connection pooling
- Session management
- Environment-based setup

---

## 🔐 Security & Authentication

### JWT Authentication Flow
1. User login with credentials
2. Backend validates and generates JWT token
3. Token stored in frontend context
4. Token included in API requests
5. Backend validates token for protected routes

### Environment Variables Security
- Sensitive keys stored in `.env` files
- Different configurations for development/production
- API keys managed through environment variables
- Database credentials secured

### File Upload Security
- File type validation
- Size limits enforcement
- Virus scanning (planned)
- Secure storage with signed URLs

---

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Python 3.9+
- Docker (optional)
- API keys for third-party services

### Installation Steps
1. Clone repository
2. Install frontend dependencies: `npm install`
3. Install backend dependencies: `pip install -r backend/requirements.txt`
4. Configure environment variables
5. Start development servers
6. Access application at `http://localhost:8080`

### Development Workflow
1. Frontend: `npm run dev` (Vite dev server)
2. Backend: `uvicorn main:app --reload` (FastAPI dev server)
3. Database: Automatic SQLite creation or PostgreSQL connection
4. Testing: `npm run test` for frontend, `pytest` for backend

---

## 📊 Key Features Summary

### 🎬 Video Processing
- AI-powered video clipping
- Automatic segment identification
- Multi-format support
- Cloud storage integration

### 🤖 AI Integration
- OpenAI for script generation
- ElevenLabs for voice synthesis
- Google Cloud TTS alternative
- Image generation for video scenes

### 📱 User Experience
- Responsive design
- Real-time processing updates
- Comprehensive analytics
- Social media integration

### 🔧 Technical Excellence
- Type-safe TypeScript
- Modern React patterns
- RESTful API design
- Containerized deployment

---

## 📁 Detailed File Documentation

### 🎨 UI Components (`src/components/ui/`)

#### `button.tsx`
**Purpose**: Reusable button component with variants
**Props**:
- `variant`: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
- `size`: "default" | "sm" | "lg" | "icon"
- `asChild`: boolean - Renders as child component
**Usage**: Primary actions, form submissions, navigation

#### `card.tsx`
**Purpose**: Container component for content sections
**Components**:
- `Card`: Main container
- `CardHeader`: Top section with title
- `CardContent`: Main content area
- `CardFooter`: Bottom action area
**Usage**: Dashboard widgets, forms, content blocks

#### `input.tsx`
**Purpose**: Form input component with validation
**Features**:
- Built-in error states
- Placeholder support
- Disabled states
- Type safety
**Usage**: Forms, search bars, data entry

#### `table.tsx`
**Purpose**: Data table components
**Components**:
- `Table`: Main table container
- `TableHeader`: Column headers
- `TableBody`: Data rows
- `TableRow`: Individual row
- `TableCell`: Cell content
**Usage**: Analytics data, video lists, user management

#### `tabs.tsx`
**Purpose**: Tabbed interface component
**Components**:
- `Tabs`: Container with state management
- `TabsList`: Tab navigation
- `TabsTrigger`: Individual tab button
- `TabsContent`: Tab panel content
**Usage**: Analytics dashboard, settings panels

### 🔧 Service Files (`src/services/`)

#### `videoProcessingService.ts`
**Purpose**: Video upload and processing API integration
**Functions**:
- `uploadVideo(file, token, options)`: Upload video to backend
- `processVideo(videoId, options)`: Trigger AI processing
- `getProcessingStatus(videoId)`: Check processing progress
- `downloadProcessedVideo(videoId)`: Get processed video URL
**Error Handling**: Retry logic, timeout handling, user feedback

#### `avatarService.ts`
**Purpose**: Avatar video creation service
**Functions**:
- `generateAvatarVideo(script, voiceType, platform)`: Create avatar video
- `getAvailableVoices()`: List voice options
- `previewAvatar(settings)`: Generate preview
**Integration**: ElevenLabs API, OpenAI image generation

#### `openAiService.ts`
**Purpose**: OpenAI API integration
**Functions**:
- `generateScript(prompt, style, length)`: Create video scripts
- `generateImage(description, style)`: Create scene images
- `optimizeContent(content, platform)`: Platform-specific optimization
**Configuration**: API key management, rate limiting

#### `transcriptionService.ts`
**Purpose**: Audio transcription service
**Functions**:
- `transcribeAudio(audioFile)`: Convert speech to text
- `getTranscriptionStatus(jobId)`: Check progress
- `formatTranscription(rawText)`: Clean and format text
**Features**: Multiple language support, timestamp extraction

### 📱 Page Components (`src/pages/`)

#### `SmartClipper.tsx`
**Purpose**: AI-powered video clipping interface
**Features**:
- Video upload with drag-and-drop
- AI segment detection and preview
- Manual clip editing tools
- Batch processing options
- Export format selection
**State Management**: Upload progress, processing status, clip library

#### `VideoCreator.tsx`
**Purpose**: Comprehensive video creation tool
**Features**:
- Multi-step creation wizard
- Template selection
- Asset management
- Real-time preview
- Collaboration tools
**Workflow**: Planning → Creation → Review → Export

#### `Settings.tsx`
**Purpose**: User preferences and configuration
**Sections**:
- Profile settings (avatar, bio, preferences)
- Account security (password, 2FA)
- API integrations (keys, webhooks)
- Billing and subscription
- Notification preferences
**Features**: Form validation, real-time updates, security checks

#### `SocialIntegration.tsx`
**Purpose**: Social media platform connections
**Platforms**:
- YouTube (upload, analytics)
- TikTok (posting, metrics)
- Instagram (stories, reels)
- LinkedIn (video posts)
- Twitter (video tweets)
**Features**: OAuth authentication, auto-posting, analytics sync

### 🔗 Integration Files (`src/integrations/`)

#### `supabase/client.ts`
**Purpose**: Supabase client configuration
**Features**:
- Authentication setup
- Database connection
- Real-time subscriptions
- File storage access
**Configuration**: Environment-based URLs, security policies

#### `supabase/types.ts`
**Purpose**: TypeScript type definitions
**Types**:
- Database schema types
- API response types
- User profile types
- Video metadata types
**Benefits**: Type safety, IDE autocomplete, error prevention

### 🎣 Custom Hooks (`src/hooks/`)

#### `use-toast.ts`
**Purpose**: Toast notification system
**Functions**:
- `toast()`: Show notification
- `toast.success()`: Success message
- `toast.error()`: Error message
- `toast.loading()`: Loading state
**Features**: Auto-dismiss, action buttons, positioning

#### `useCloudinary.ts`
**Purpose**: Cloudinary integration hook
**Functions**:
- `uploadImage()`: Upload and transform images
- `uploadVideo()`: Upload and process videos
- `getOptimizedUrl()`: Generate optimized URLs
**Features**: Progress tracking, error handling, transformation options

#### `use-mobile.tsx`
**Purpose**: Responsive design hook
**Returns**: Boolean indicating mobile viewport
**Usage**: Conditional rendering, mobile-specific features
**Implementation**: Window resize listener, breakpoint detection

---

## 🐍 Backend File Details

### 🔧 Core Backend Files

#### `backend/main.py` (Detailed Breakdown)
**Lines 1-50**: Imports and setup
- FastAPI app initialization
- CORS middleware configuration
- Database connection setup
- Authentication imports

**Lines 51-150**: Authentication system
- JWT token creation and validation
- User registration and login endpoints
- Password hashing and verification
- OAuth2 integration

**Lines 151-300**: Video processing endpoints
- File upload handling
- AI processing triggers
- Status checking endpoints
- Download and streaming

**Lines 301-450**: AI integration endpoints
- OpenAI script generation
- ElevenLabs voice synthesis
- Google Cloud TTS integration
- Image generation

**Lines 451-600**: User management
- Profile CRUD operations
- Subscription handling
- Admin functions
- Analytics endpoints

#### `backend/video_processing.py` (Detailed Functions)
**`transcribe_video()`**:
- Extracts audio from video using MoviePy
- Uses speech recognition for transcription
- Returns text with timestamps
- Handles multiple languages

**`identify_segments()`**:
- Analyzes transcript for engaging content
- Uses AI to score segments
- Identifies optimal clip points
- Returns segment metadata

**`extract_clips()`**:
- Creates video clips from segments
- Applies platform-specific formatting
- Generates thumbnails
- Uploads to cloud storage

**`process_video_pipeline()`**:
- Orchestrates complete processing workflow
- Handles error recovery
- Updates processing status
- Sends completion notifications

#### `backend/storage.py` (Storage Management)
**Cloudinary Integration**:
- Video upload and transformation
- Automatic optimization
- CDN delivery
- Analytics tracking

**AWS S3 Support**:
- Bucket management
- Signed URL generation
- Lifecycle policies
- Cost optimization

**Local Storage Fallback**:
- Development environment support
- File system management
- Temporary file cleanup
- Security considerations

### 🗄️ Database Schema (`backend/models.py`)

#### User Model (Extended)
```python
class User(Base):
    __tablename__ = "users"

    # Core fields
    id = Column(Integer, primary_key=True)
    username = Column(String, unique=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)

    # Profile fields
    avatar_url = Column(String, nullable=True)
    bio = Column(String, nullable=True)
    subscription = Column(String, default="free")

    # Metadata
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, onupdate=func.now())

    # Relationships
    videos = relationship("Video", back_populates="user")
    clips = relationship("VideoClip", back_populates="user")
```

#### Video Model (Extended)
```python
class Video(Base):
    __tablename__ = "videos"

    # Core fields
    id = Column(Integer, primary_key=True)
    title = Column(String, nullable=False)
    description = Column(Text)
    file_path = Column(String, nullable=False)

    # Metadata
    duration = Column(Float)
    file_size = Column(Integer)
    format = Column(String)
    resolution = Column(String)

    # Processing
    status = Column(String, default="uploaded")
    processing_progress = Column(Integer, default=0)
    error_message = Column(Text)

    # Relationships
    user_id = Column(Integer, ForeignKey("users.id"))
    user = relationship("User", back_populates="videos")
    clips = relationship("VideoClip", back_populates="video")
```

---

## 🔧 Configuration Deep Dive

### Frontend Configuration Files

#### `vite.config.ts` (Build Configuration)
```typescript
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 8080,
    proxy: {
      "/api": "http://localhost:8000"
    }
  },
  build: {
    outDir: "dist",
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ["react", "react-dom"],
          ui: ["@radix-ui/react-dialog", "@radix-ui/react-dropdown-menu"]
        }
      }
    }
  }
})
```

#### `tailwind.config.ts` (Styling Configuration)
```typescript
module.exports = {
  content: ["./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: "hsl(var(--primary))",
        secondary: "hsl(var(--secondary))",
        accent: "hsl(var(--accent))",
      },
      animation: {
        "fade-in": "fadeIn 0.5s ease-in-out",
        "slide-up": "slideUp 0.3s ease-out",
      }
    }
  },
  plugins: [require("tailwindcss-animate")]
}
```

### Backend Configuration

#### Environment Variables (`.env`)
```bash
# Authentication
SECRET_KEY=your_jwt_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=1440

# Database
DATABASE_URL=postgresql://user:pass@localhost/smartclips

# AI Services
OPENAI_API_KEY=sk-proj-your_openai_key
ELEVENLABS_API_KEY=sk_your_elevenlabs_key

# Storage
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret

# AWS (Optional)
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
AWS_BUCKET_NAME=your_bucket_name
AWS_REGION=us-east-1

# Google Cloud
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
```

This comprehensive documentation covers every major file and component in the SmartClips project, providing developers with detailed understanding of the architecture, functionality, and implementation details.
