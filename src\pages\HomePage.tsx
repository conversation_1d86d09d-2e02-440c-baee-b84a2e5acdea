
import React from "react";
import { Navigate } from "react-router-dom";
import DashboardLayout from "@/components/Dashboard";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Scissors,
  VideoIcon,
  BookOpen,
  BarChart,
  Calendar,
  Settings,
  Image,
  Share2,
  Cloud,
  TrendingUp,
  Users,
  Play
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";

const HomePage = () => {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated) {
    return <Navigate to="/landing" />;
  }

  const featuredTools = [
    {
      title: "Smart Clipper",
      description: "Extract the most interesting segments from your videos",
      icon: Scissors,
      link: "/smart-clipper",
      badge: "Popular"
    },
    {
      title: "Avatar Creator",
      description: "Create engaging avatar videos with AI-generated visuals",
      icon: VideoIcon,
      link: "/avatar-creator",
      badge: "New"
    },
    {
      title: "Script Generator",
      description: "Generate compelling scripts and turn them into videos",
      icon: BookOpen,
      link: "/script-generator",
      badge: "AI Powered"
    }
  ];

  const quickActions = [
    {
      title: "Analytics",
      description: "View your performance metrics",
      icon: BarChart,
      link: "/analytics"
    },
    {
      title: "Gallery",
      description: "Browse your created content",
      icon: Image,
      link: "/gallery"
    },
    {
      title: "Calendar",
      description: "Schedule your content",
      icon: Calendar,
      link: "/calendar"
    },
    {
      title: "Social Integration",
      description: "Connect your social accounts",
      icon: Share2,
      link: "/social-integration"
    }
  ];

  const stats = [
    {
      title: "Videos Created",
      value: "0",
      icon: VideoIcon,
      color: "text-blue-500"
    },
    {
      title: "Total Views",
      value: "0",
      icon: Play,
      color: "text-green-500"
    },
    {
      title: "Credits Available",
      value: user?.credits || 0,
      icon: TrendingUp,
      color: "text-purple-500"
    }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Welcome back, {user?.username || 'User'}!</h1>
            <p className="text-muted-foreground mt-1">
              Your all-in-one AI video creation platform
            </p>
          </div>
          <Link to="/smart-clipper">
            <Button size="lg" className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              Create New Video
            </Button>
          </Link>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                  </div>
                  <div className={`h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Featured Tools */}
        <div>
          <h2 className="text-2xl font-bold mb-4">Featured Tools</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {featuredTools.map((tool) => (
              <Card key={tool.title} className="hover:border-primary/50 transition-colors relative">
                <CardContent className="p-6">
                  {tool.badge && (
                    <Badge className="absolute top-4 right-4" variant="secondary">
                      {tool.badge}
                    </Badge>
                  )}
                  <div className="mb-4 rounded-full bg-primary/10 p-3 w-fit">
                    <tool.icon className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{tool.title}</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    {tool.description}
                  </p>
                  <Link to={tool.link}>
                    <Button variant="outline" size="sm" className="w-full">
                      Start Using
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-2xl font-bold mb-4">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <Link key={action.title} to={action.link}>
                <Card className="hover:border-primary/50 transition-colors cursor-pointer">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center">
                        <action.icon className="h-5 w-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-medium">{action.title}</h3>
                        <p className="text-xs text-muted-foreground">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>

        {/* Getting Started */}
        <Card>
          <CardHeader>
            <CardTitle>Getting Started with SmartClips</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              Welcome to SmartClips! Here's how to get the most out of our AI-powered video platform:
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-blue-500 text-white text-xs flex items-center justify-center font-bold">1</div>
                  <h4 className="font-medium">Upload & Clip</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Use Smart Clipper to automatically extract engaging segments from your videos
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-purple-500 text-white text-xs flex items-center justify-center font-bold">2</div>
                  <h4 className="font-medium">Create Avatars</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Generate AI-powered avatar videos with custom scripts and visuals
                </p>
              </div>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <div className="h-6 w-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center font-bold">3</div>
                  <h4 className="font-medium">Share & Analyze</h4>
                </div>
                <p className="text-sm text-muted-foreground">
                  Share your content and track performance with detailed analytics
                </p>
              </div>
            </div>
            <div className="flex gap-2 pt-4">
              <Link to="/smart-clipper">
                <Button>Get Started</Button>
              </Link>
              <Link to="/analytics">
                <Button variant="outline">View Analytics</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default HomePage;
