import React, { useState } from "react";
import DashboardLayout from "@/components/Dashboard";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  Video,
  Eye,
  Clock,
  Download,
  Share2,
  MoreHorizontal,
  Calendar,
  Filter,
  Play,
  Heart,
  MessageCircle,
  Repeat2,
  ExternalLink,
  Target,
  MousePointer,
  Zap,
  BarChart3,
  TrendingUpIcon
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const Analytics = () => {
  const [activeTab, setActiveTab] = useState("video-performance");
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState("views");

  // Video Performance Stats
  const videoStats = [
    {
      title: "Total Views",
      value: "2.4M",
      change: "+12.5% from last month",
      icon: Eye,
      trend: "up",
      percentage: "+12.5%"
    },
    {
      title: "Engagement Rate",
      value: "8.2%",
      change: "****% from last month",
      icon: Heart,
      trend: "up",
      percentage: "****%"
    },
    {
      title: "Click-Through Rate",
      value: "3.7%",
      change: "+0.8% from last month",
      icon: MousePointer,
      trend: "up",
      percentage: "+0.8%"
    },
    {
      title: "Conversion Rate",
      value: "2.1%",
      change: "-0.3% from last month",
      icon: Target,
      trend: "down",
      percentage: "-0.3%"
    }
  ];

  // Affiliate Stats
  const affiliateStats = [
    {
      title: "Total Revenue",
      value: "$0.00",
      change: "All clicks from your links",
      icon: DollarSign,
      trend: "up",
      percentage: "0%"
    },
    {
      title: "Total Videos",
      value: "0",
      change: "All videos from your links",
      icon: Video,
      trend: "neutral",
      percentage: "0%"
    },
    {
      title: "Total Views",
      value: "0",
      change: "All views from your links",
      icon: Eye,
      trend: "neutral",
      percentage: "0%"
    },
    {
      title: "Total Referred Customers",
      value: "0",
      change: "All customers from your links",
      icon: Users,
      trend: "neutral",
      percentage: "0%"
    }
  ];

  // Video Performance Data
  const videoPerformanceData = [
    {
      id: 1,
      title: "Product Demo - iPhone 15 Pro",
      thumbnail: "https://images.unsplash.com/photo-1611162617213-7d7a39e9b1d7?q=80&w=150&h=85&auto=format&fit=crop",
      platform: "YouTube",
      views: 125000,
      likes: 8500,
      comments: 420,
      shares: 1200,
      saves: 890,
      clickThroughRate: 4.2,
      engagementRate: 8.7,
      watchTime: "3:45",
      avgViewDuration: "2:30",
      impressions: 450000,
      ctr: 3.8,
      revenue: 2450.00,
      cpm: 12.50,
      createdAt: "2024-01-15",
      status: "Published"
    },
    {
      id: 2,
      title: "Tutorial: Advanced Video Editing",
      thumbnail: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=150&h=85&auto=format&fit=crop",
      platform: "TikTok",
      views: 89000,
      likes: 12000,
      comments: 890,
      shares: 2100,
      saves: 1500,
      clickThroughRate: 5.1,
      engagementRate: 18.2,
      watchTime: "0:58",
      avgViewDuration: "0:45",
      impressions: 320000,
      ctr: 4.5,
      revenue: 890.00,
      cpm: 8.90,
      createdAt: "2024-01-14",
      status: "Published"
    },
    {
      id: 3,
      title: "Brand Story - Behind the Scenes",
      thumbnail: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=150&h=85&auto=format&fit=crop",
      platform: "Instagram",
      views: 67000,
      likes: 5600,
      comments: 340,
      shares: 780,
      saves: 1200,
      clickThroughRate: 3.9,
      engagementRate: 12.1,
      watchTime: "1:30",
      avgViewDuration: "1:15",
      impressions: 180000,
      ctr: 3.2,
      revenue: 1200.00,
      cpm: 15.20,
      createdAt: "2024-01-13",
      status: "Published"
    },
    {
      id: 4,
      title: "Client Testimonial Video",
      thumbnail: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=150&h=85&auto=format&fit=crop",
      platform: "LinkedIn",
      views: 34000,
      likes: 2100,
      comments: 180,
      shares: 450,
      saves: 320,
      clickThroughRate: 6.2,
      engagementRate: 9.1,
      watchTime: "2:15",
      avgViewDuration: "1:50",
      impressions: 95000,
      ctr: 5.8,
      revenue: 680.00,
      cpm: 18.50,
      createdAt: "2024-01-12",
      status: "Published"
    },
    {
      id: 5,
      title: "Quick Tips: Social Media Growth",
      thumbnail: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?q=80&w=150&h=85&auto=format&fit=crop",
      platform: "YouTube Shorts",
      views: 156000,
      likes: 18500,
      comments: 1200,
      shares: 3400,
      saves: 2100,
      clickThroughRate: 7.8,
      engagementRate: 16.4,
      watchTime: "0:45",
      avgViewDuration: "0:38",
      impressions: 580000,
      ctr: 6.2,
      revenue: 1850.00,
      cpm: 9.80,
      createdAt: "2024-01-11",
      status: "Published"
    }
  ];

  const recentVideos = [
    {
      id: 1,
      title: "Sample Video 1",
      status: "Processing",
      views: 0,
      duration: "0:00",
      createdAt: "May 28, 2024",
      commission: "$0.00"
    }
  ];

  const links = [
    {
      id: 1,
      url: "https://smartclips.io/ref/user123",
      clicks: 0,
      commission: "$0.00",
      customers: 0,
      status: "Active"
    }
  ];

  const filteredVideos = videoPerformanceData.filter(video =>
    video.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const sortedVideos = [...filteredVideos].sort((a, b) => {
    switch (sortBy) {
      case "views":
        return b.views - a.views;
      case "engagement":
        return b.engagementRate - a.engagementRate;
      case "revenue":
        return b.revenue - a.revenue;
      case "ctr":
        return b.clickThroughRate - a.clickThroughRate;
      default:
        return 0;
    }
  });

  const formatNumber = (num: number) => {
    if (num >= 1000000) return (num / 1000000).toFixed(1) + 'M';
    if (num >= 1000) return (num / 1000).toFixed(1) + 'K';
    return num.toString();
  };

  const getPlatformBadge = (platform: string) => {
    const colors = {
      YouTube: "bg-red-100 text-red-800",
      TikTok: "bg-black text-white",
      Instagram: "bg-pink-100 text-pink-800",
      LinkedIn: "bg-blue-100 text-blue-800",
      "YouTube Shorts": "bg-red-100 text-red-800"
    };
    return <Badge className={colors[platform] || "bg-gray-100 text-gray-800"}>{platform}</Badge>;
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
            <p className="text-muted-foreground mt-1">
              Track your video performance and affiliate metrics
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              Last 30 days
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="video-performance" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Video Performance
            </TabsTrigger>
            <TabsTrigger value="affiliate-analytics" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Affiliate Analytics
            </TabsTrigger>
          </TabsList>

          {/* Video Performance Tab */}
          <TabsContent value="video-performance" className="space-y-6">
            {/* Video Performance Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {videoStats.map((stat, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {stat.title}
                        </p>
                        <p className="text-2xl font-bold">{stat.value}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {stat.change}
                        </p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                        <stat.icon className="h-6 w-6 text-primary" />
                      </div>
                    </div>
                    <div className="flex items-center mt-4">
                      {stat.trend === "up" ? (
                        <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      ) : stat.trend === "down" ? (
                        <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                      ) : null}
                      <span className={`text-sm ${
                        stat.trend === "up" ? "text-green-500" :
                        stat.trend === "down" ? "text-red-500" :
                        "text-muted-foreground"
                      }`}>
                        {stat.percentage}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Video Performance Table */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Video Performance</CardTitle>
                <div className="flex items-center gap-2">
                  <Input
                    placeholder="Search videos..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-64"
                  />
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="views">Views</SelectItem>
                      <SelectItem value="engagement">Engagement</SelectItem>
                      <SelectItem value="revenue">Revenue</SelectItem>
                      <SelectItem value="ctr">CTR</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Video</TableHead>
                      <TableHead>Platform</TableHead>
                      <TableHead>Views</TableHead>
                      <TableHead>Engagement</TableHead>
                      <TableHead>CTR</TableHead>
                      <TableHead>Revenue</TableHead>
                      <TableHead>CPM</TableHead>
                      <TableHead></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedVideos.map((video) => (
                      <TableRow key={video.id}>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <img
                              src={video.thumbnail}
                              alt={video.title}
                              className="w-16 h-9 object-cover rounded"
                            />
                            <div>
                              <p className="font-medium">{video.title}</p>
                              <p className="text-sm text-muted-foreground">
                                {video.watchTime} • {new Date(video.createdAt).toLocaleDateString()}
                              </p>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>{getPlatformBadge(video.platform)}</TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{formatNumber(video.views)}</p>
                            <p className="text-sm text-muted-foreground">
                              {formatNumber(video.impressions)} impressions
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{video.engagementRate}%</p>
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              <span className="flex items-center gap-1">
                                <Heart className="h-3 w-3" />
                                {formatNumber(video.likes)}
                              </span>
                              <span className="flex items-center gap-1">
                                <MessageCircle className="h-3 w-3" />
                                {formatNumber(video.comments)}
                              </span>
                              <span className="flex items-center gap-1">
                                <Share2 className="h-3 w-3" />
                                {formatNumber(video.shares)}
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{video.clickThroughRate}%</p>
                            <p className="text-sm text-muted-foreground">
                              Avg: {video.avgViewDuration}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <p className="font-medium">${video.revenue.toFixed(2)}</p>
                        </TableCell>
                        <TableCell>
                          <p className="font-medium">${video.cpm.toFixed(2)}</p>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent>
                              <DropdownMenuItem>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Open Video
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="h-4 w-4 mr-2" />
                                Export Data
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Affiliate Analytics Tab */}
          <TabsContent value="affiliate-analytics" className="space-y-6">
            {/* Affiliate Stats Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {affiliateStats.map((stat, index) => (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">
                          {stat.title}
                        </p>
                        <p className="text-2xl font-bold">{stat.value}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {stat.change}
                        </p>
                      </div>
                      <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                        <stat.icon className="h-6 w-6 text-primary" />
                      </div>
                    </div>
                    <div className="flex items-center mt-4">
                      {stat.trend === "up" ? (
                        <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                      ) : stat.trend === "down" ? (
                        <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                      ) : null}
                      <span className={`text-sm ${
                        stat.trend === "up" ? "text-green-500" :
                        stat.trend === "down" ? "text-red-500" :
                        "text-muted-foreground"
                      }`}>
                        {stat.percentage}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Links Section */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Affiliate Links</CardTitle>
                <Button size="sm">Create Link</Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground">
                    Links - Clicks per link in seconds starting from 2024-12-30
                  </div>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Link</TableHead>
                        <TableHead>Clicks</TableHead>
                        <TableHead>Commission</TableHead>
                        <TableHead>Customers</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead></TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {links.map((link) => (
                        <TableRow key={link.id}>
                          <TableCell className="font-medium">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                              {link.url}
                            </div>
                          </TableCell>
                          <TableCell>{link.clicks}</TableCell>
                          <TableCell>{link.commission}</TableCell>
                          <TableCell>{link.customers}</TableCell>
                          <TableCell>
                            <Badge variant="secondary">{link.status}</Badge>
                          </TableCell>
                          <TableCell>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>

            {/* Recent Referrals */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Referrals</CardTitle>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Referral</TableHead>
                      <TableHead>Amount Paid</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>View All</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    <TableRow>
                      <TableCell colSpan={4} className="text-center text-muted-foreground py-8">
                        No referrals yet
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Analytics;
