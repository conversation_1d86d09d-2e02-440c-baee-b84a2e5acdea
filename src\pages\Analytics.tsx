import React from "react";
import DashboardLayout from "@/components/Dashboard";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  Video, 
  Eye,
  Clock,
  Download,
  Share2,
  MoreHorizontal,
  Calendar,
  Filter
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const Analytics = () => {
  // Mock data for analytics
  const stats = [
    {
      title: "Total Revenue",
      value: "$0.00",
      change: "All clicks from your links",
      icon: DollarSign,
      trend: "up",
      percentage: "0%"
    },
    {
      title: "Total Videos",
      value: "0",
      change: "All videos from your links",
      icon: Video,
      trend: "neutral",
      percentage: "0%"
    },
    {
      title: "Total Views",
      value: "0",
      change: "All views from your links",
      icon: Eye,
      trend: "neutral",
      percentage: "0%"
    },
    {
      title: "Total Referred Customers",
      value: "0",
      change: "All customers from your links",
      icon: Users,
      trend: "neutral",
      percentage: "0%"
    }
  ];

  const recentVideos = [
    {
      id: 1,
      title: "Sample Video 1",
      status: "Processing",
      views: 0,
      duration: "0:00",
      createdAt: "May 28, 2024",
      commission: "$0.00"
    }
  ];

  const links = [
    {
      id: 1,
      url: "https://smartclips.io/ref/user123",
      clicks: 0,
      commission: "$0.00",
      customers: 0,
      status: "Active"
    }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Good afternoon, User!</h1>
            <p className="text-muted-foreground mt-1">
              You are SmartClips's Affiliate Program
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              Last 30 days
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      {stat.title}
                    </p>
                    <p className="text-2xl font-bold">{stat.value}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {stat.change}
                    </p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <stat.icon className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <div className="flex items-center mt-4">
                  {stat.trend === "up" ? (
                    <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                  ) : stat.trend === "down" ? (
                    <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
                  ) : null}
                  <span className={`text-sm ${
                    stat.trend === "up" ? "text-green-500" : 
                    stat.trend === "down" ? "text-red-500" : 
                    "text-muted-foreground"
                  }`}>
                    {stat.percentage}
                  </span>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Links Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Links</CardTitle>
            <Button size="sm">Create Link</Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm text-muted-foreground">
                Links - Clicks per link in seconds starting from 2024-12-30
              </div>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Link</TableHead>
                    <TableHead>Clicks</TableHead>
                    <TableHead>Commission</TableHead>
                    <TableHead>Customers</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {links.map((link) => (
                    <TableRow key={link.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          {link.url}
                        </div>
                      </TableCell>
                      <TableCell>{link.clicks}</TableCell>
                      <TableCell>{link.commission}</TableCell>
                      <TableCell>{link.customers}</TableCell>
                      <TableCell>
                        <Badge variant="secondary">{link.status}</Badge>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Recent Referrals */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Referrals</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Referral</TableHead>
                  <TableHead>Amount Paid</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>View All</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell colSpan={4} className="text-center text-muted-foreground py-8">
                    No referrals yet
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Analytics;
